
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Check, ChevronRight, Star, Sparkles, Crown, Zap, FileSignatureIcon, UnlockIcon, OptionIcon, PlusIcon, SparklesIcon, WandSparklesIcon } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { motion } from "framer-motion";
import { cn } from '@/lib/utils';
/**
 * @typedef {Object} PricingPlanProps
 * @property {string} id
 * @property {string} name
 * @property {string} price
 * @property {string} [originalPrice]
 * @property {string[]} features
 * @property {string[]} [perks]
 * @property {number} index
 * @property {boolean} [recommended]
 * @property {boolean} [mostPopular]
 * @property {string|null} [badge]
 * @property {string|null} [discountPercentage]
 * @property {string} [validityPeriod]
 * @property {() => void} onChoosePlan
 * @property {'plus'|'pro'|'max'|'basic'} [variant]
 */
const formatPrice = (priceStr) => {
  const priceNum = parseFloat(priceStr);
  return priceNum % 1 === 0 ? priceNum.toString() : priceStr;
};
const PricingCard = ({
  name,
  price,
  originalPrice,
  features,
  perks = [],
  index,
  recommended = false,
  mostPopular = false,
  badge = null,
  discountPercentage = null,
  validityPeriod,
  onChoosePlan,
  variant = 'plus'
}) => {
  const getVariantStyles = () => {
    switch (variant) {
      case 'max':
        return {
          gradient: 'from-purple-600 via-purple-500 to-indigo-600',
          textColor: 'text-purple-400',
          borderColor: 'border-purple-500/30',
          glowColor: 'shadow-purple-500/25',
          bgGradient: 'from-purple-900/20 via-purple-800/10 to-indigo-900/20',
        };
      case 'pro':
        return {
          gradient: 'from-blue-600 via-blue-500 to-cyan-600',
          textColor: 'text-blue-400',
          borderColor: 'border-blue-500/30',
          glowColor: 'shadow-blue-500/25',
          bgGradient: 'from-blue-900/20 via-blue-800/10 to-cyan-900/20',
        };
      case 'basic':
        return {
          gradient: 'from-pegasus-blue-600 via-pegasus-blue-500 to-pegasus-blue-400',
          textColor: 'text-pegasus-blue-400',
          borderColor: 'border-pegasus-blue-500/30',
          glowColor: 'shadow-pegasus-blue-500/25',
          bgGradient: 'from-pegasus-blue-900/20 via-pegasus-blue-800/10 to-pegasus-blue-700/20',
        };
      default:
        return {
          gradient: 'from-orange-600 via-orange-500 to-amber-600',
          textColor: 'text-orange-400',
          borderColor: 'border-orange-500/30',
          glowColor: 'shadow-orange-500/25',
          bgGradient: 'from-orange-900/20 via-orange-800/10 to-amber-900/20',
        };
    }
  };
  
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        delay: index * 0.1,
        ease: "easeOut"
      }
    },
    hover: {
      y: -8,
      transition: { duration: 0.2, ease: "easeOut" }
    }
  };

  const variantStyles = getVariantStyles();

  return (
    <motion.div
      className="h-full"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
      whileHover="hover"
    >
      <Card
        className={cn(
          "h-full flex flex-col overflow-hidden relative group",
          "bg-gradient-to-br from-[#1a1a1a] to-[#1e1e1e]",
          "border border-gray-700/50 rounded-xl",
          "transition-all duration-300 ease-out",
          "hover:shadow-xl hover:border-gray-600/70",
          mostPopular && [
            "ring-1 ring-orange-400/30 border-orange-400/50",
            "shadow-lg shadow-orange-500/10",
            "scale-105"
          ],
          recommended && [
            "ring-1 ring-purple-400/30 border-purple-400/50",
            "shadow-lg shadow-purple-500/10",
            "scale-105"
          ]
        )}
      >
        {/* Subtle Background Overlay */}
        <div className={cn(
          "absolute inset-0 opacity-0 group-hover:opacity-50 transition-opacity duration-300",
          `bg-gradient-to-br ${variantStyles.bgGradient}`,
          "rounded-xl"
        )} />

        {/* Top Accent Line */}
        <div className={cn(
          "h-1 w-full bg-gradient-to-r",
          variantStyles.gradient
        )} />

        {/* Badge for Most Popular, Recommended, etc. */}
        {badge && (
          <div className={cn(
            "absolute top-4 right-4 z-30",
            "px-3 py-1.5 rounded-lg text-xs font-semibold",
            "border shadow-lg",
            "flex items-center gap-1.5",
            mostPopular && [
              "bg-gradient-to-r from-orange-500 to-red-500 text-white",
              "border-orange-400/50 shadow-orange-500/20"
            ],
            recommended && [
              "bg-gradient-to-r from-purple-500 to-indigo-600 text-white",
              "border-purple-400/50 shadow-purple-500/20"
            ],
            !mostPopular && !recommended && [
              "bg-gradient-to-r from-blue-500 to-cyan-500 text-white",
              "border-blue-400/50 shadow-blue-500/20"
            ]
          )}
          >
            {badge}
          </div>
        )}

        {/* Discount Badge */}
        {discountPercentage && (
          <div className={cn(
            "absolute top-4 left-4 z-20",
            "px-2.5 py-1 rounded-md text-xs font-semibold",
            "bg-gradient-to-r from-red-500 to-red-600 text-white",
            "border border-red-400/50 shadow-lg shadow-red-500/20"
          )}>
            {discountPercentage} OFF
          </div>
        )}
        <CardHeader className="pt-20 pb-6 relative z-10">
          <div className="flex items-center justify-between mb-3">
             <CardTitle className={cn(
               "text-3xl font-bold",
                 variantStyles.textColor
              )}>
                 {name}
             </CardTitle>
  <div className="text-center">
    <span className="text-sm text-gray-400 font-medium px-3 py-2 bg-gray-800/50 rounded-full border border-gray-700/50">
      {validityPeriod || 'Per Month'}
    </span>
  </div>
</div>
          {/* Price Section */}
          <div className="text-center">
            <div className="flex items-baseline mb-2">
               {originalPrice && (
                  <span className="text-lg text-gray-500 font-normal line-through mr-1">
                   ${formatPrice(originalPrice)}
                  </span>
         )}
                  <span className="text-3xl font-bold text-white">
                   ${formatPrice(price)}
                  </span>
            </div>
          </div>
        </CardHeader>
        <CardContent className="px-6 pb-6 flex-grow relative z-10">
          {/* Features List */}
          <ul className="space-y-3 mb-6"> 
                          <h4 className={cn(
                "font-semibold mb-3 flex items-center gap-2",
                variantStyles.textColor
              )}>
                <WandSparklesIcon className="h-4 w-4" />
                Features
              </h4>
            {features.map((feature, i) => (
              <li key={i} className="flex items-start">
                <div className={cn(
                  "h-5 w-5 mr-3 shrink-0 mt-0.5 rounded-full",
                  "flex items-center justify-center",
                  "bg-gradient-to-br", variantStyles.bgGradient,
                  "border", variantStyles.borderColor
                )}>
                  <Check className={cn("h-3 w-3", variantStyles.textColor)} strokeWidth={3} />
                </div>
                <span className="text-gray-300 leading-relaxed">{feature}</span>
              </li>
            ))}
          </ul>

          {/* Extra Perks */}
          {perks.length > 0 && (
            <div className="mt-6 pt-4 border-t border-gray-700/50">
              <h4 className={cn(
                "font-semibold mb-3 flex items-center gap-2",
                variantStyles.textColor
              )}>
                <SparklesIcon className="h-4 w-4" />
                Extra Perks
              </h4>
              <ul className="space-y-2">
                {perks.map((perk, i) => (
                  <li key={i} className="flex items-start">
                <div className={cn(
                  "h-4 w-4 mr-3 shrink-0 mt-0.5 rounded-full",
                  "flex items-center justify-center",
                  "bg-gradient-to-br", variantStyles.bgGradient,
                  "border", variantStyles.borderColor
                )}>
                  <Check className={cn("h-3 w-3", variantStyles.textColor)} strokeWidth={3} />
                </div>
                    <span className="text-gray-400 text-sm leading-relaxed">{perk}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </CardContent>
        <CardFooter className="px-6 py-4 mt-auto relative z-10">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                className={cn(
                  "w-full h-11 font-semibold text-white shadow-lg",
                  "bg-gradient-to-r", variantStyles.gradient,
                  "hover:shadow-xl hover:opacity-90",
                  "transition-all duration-300 ease-out"
                )}
              >
                Choose Plan
                <ChevronRight className="h-4 w-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="w-48 bg-[#1a1a1a] border border-gray-700/50"
            >
              <DropdownMenuItem
                onClick={onChoosePlan}
                className="cursor-pointer text-gray-300 hover:text-white hover:bg-gray-800/50"
              >
                <Zap className="h-4 w-4 mr-2" />
                Buy Now
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => window.location.href = '/resellers'}
                className="cursor-pointer text-gray-300 hover:text-white hover:bg-gray-800/50"
              >
                <Star className="h-4 w-4 mr-2" />
                Contact Sales
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => window.location.href = '/faq'}
                className="cursor-pointer text-gray-300 hover:text-white hover:bg-gray-800/50"
              >
                <Sparkles className="h-4 w-4 mr-2" />
                Read FAQ
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardFooter>
      </Card>
    </motion.div>
  );
};

export default PricingCard;
